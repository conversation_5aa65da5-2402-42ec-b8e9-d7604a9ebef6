import torch
import torch.nn as nn
import torch.nn.functional as F
from gcn_feature_extractor import (GCNFeatureExtractor, create_gcn_feature_extractor,
                                  GATFeatureExtractor, create_gat_feature_extractor)
from vgg3d_feature_extractor import VGG3DFeatureExtractor, create_vgg3d_feature_extractor
from bilinear_attention_fusion import BilinearAttentionFusion

class MultiModalClassifier(nn.Module):
    """
    多模态分类器，结合fMRI图数据和sMRI 3D图像数据
    """
    def __init__(self,
                 # 图神经网络参数
                 num_node_features,
                 graph_hidden_channels=64,
                 graph_feature_dim=128,
                 graph_num_layers=3,
                 graph_dropout=0.5,
                 # GAT特定参数
                 use_gat=True,  # 是否使用GAT而不是GCN
                 gat_heads=8,
                 gat_concat=True,
                 edge_dim=1,  # 边特征维度
                 # VGG参数
                 vgg_model_name="vgg16",
                 vgg_in_channels=1,
                 vgg_feature_dim=128,
                 # 融合参数
                 fusion_dim=256,
                 attention_dim=128,
                 fusion_dropout=0.3,
                 # 分类参数
                 num_classes=2,
                 final_dropout=0.5):
        """
        初始化多模态分类器

        参数:
        - num_node_features: 图神经网络节点特征维度
        - graph_hidden_channels: 图神经网络隐藏层维度
        - graph_feature_dim: 图神经网络输出特征维度
        - graph_num_layers: 图神经网络层数
        - graph_dropout: 图神经网络dropout率
        - use_gat: 是否使用GAT而不是GCN
        - gat_heads: GAT注意力头数
        - gat_concat: GAT是否拼接多头结果
        - edge_dim: 边特征维度
        - vgg_model_name: VGG模型名称
        - vgg_in_channels: VGG输入通道数
        - vgg_feature_dim: VGG输出特征维度
        - fusion_dim: 融合层维度
        - attention_dim: 注意力维度
        - fusion_dropout: 融合层dropout率
        - num_classes: 分类类别数
        - final_dropout: 最终分类层dropout率
        """
        super(MultiModalClassifier, self).__init__()

        # 保存参数
        self.num_classes = num_classes
        self.graph_feature_dim = graph_feature_dim
        self.vgg_feature_dim = vgg_feature_dim
        self.use_gat = use_gat

        # 图神经网络特征提取器
        if use_gat:
            self.graph_extractor = create_gat_feature_extractor(
                num_node_features=num_node_features,
                hidden_channels=graph_hidden_channels,
                feature_dim=graph_feature_dim,
                num_layers=graph_num_layers,
                dropout=graph_dropout,
                heads=gat_heads,
                concat=gat_concat,
                edge_dim=edge_dim
            )
        else:
            self.graph_extractor = create_gcn_feature_extractor(
                num_node_features=num_node_features,
                hidden_channels=graph_hidden_channels,
                feature_dim=graph_feature_dim,
                num_layers=graph_num_layers,
                dropout=graph_dropout
            )
        
        # 3D VGG特征提取器
        self.vgg_extractor = create_vgg3d_feature_extractor(
            model_name=vgg_model_name,
            in_channels=vgg_in_channels,
            feature_dim=vgg_feature_dim,
            init_weights=True
        )
        
        # 双线性注意力融合模块
        self.fusion_module = BilinearAttentionFusion(
            feature_dim1=graph_feature_dim,
            feature_dim2=vgg_feature_dim,
            fusion_dim=fusion_dim,
            attention_dim=attention_dim,
            dropout=fusion_dropout
        )

        # 最终分类层
        self.final_classifier = nn.Sequential(
            nn.Linear(fusion_dim // 2, fusion_dim // 4),
            nn.ReLU(),
            nn.Dropout(final_dropout),
            nn.Linear(fusion_dim // 4, num_classes)
        )

        # 单模态分类器（用于消融研究）
        self.graph_classifier = nn.Linear(graph_feature_dim, num_classes)
        self.vgg_classifier = nn.Linear(vgg_feature_dim, num_classes)
        
    def forward(self, fmri_data, smri_data, return_features=False, use_single_modality=None):
        """
        前向传播

        参数:
        - fmri_data: fMRI图数据（包含x, edge_index, batch, edge_attr）
        - smri_data: sMRI 3D图像数据 [batch_size, channels, depth, height, width]
        - return_features: 是否返回中间特征
        - use_single_modality: 使用单一模态 ('graph', 'vgg', None)

        返回:
        - logits: 分类logits
        - 如果return_features=True，还返回中间特征和注意力权重
        """
        # 提取图神经网络特征
        if self.use_gat and hasattr(fmri_data, 'edge_attr'):
            # 使用GAT并传入边权重
            graph_features = self.graph_extractor(
                fmri_data.x,
                fmri_data.edge_index,
                fmri_data.batch,
                edge_attr=fmri_data.edge_attr,
                return_features=True
            )
        else:
            # 使用GCN或GAT但不使用边权重
            graph_features = self.graph_extractor(
                fmri_data.x,
                fmri_data.edge_index,
                fmri_data.batch,
                return_features=True
            )  # [batch_size, graph_feature_dim]

        # 提取VGG特征
        vgg_features = self.vgg_extractor(
            smri_data,
            return_features=True
        )  # [batch_size, vgg_feature_dim]

        # 单模态分类（用于消融研究）
        if use_single_modality == 'graph':
            logits = self.graph_classifier(graph_features)
            if return_features:
                return logits, {'graph_features': graph_features}
            return logits
        elif use_single_modality == 'vgg':
            logits = self.vgg_classifier(vgg_features)
            if return_features:
                return logits, {'vgg_features': vgg_features}
            return logits

        # 多模态融合
        fused_features, attention_weights = self.fusion_module(graph_features, vgg_features)

        # 最终分类
        logits = self.final_classifier(fused_features)

        if return_features:
            features_dict = {
                'graph_features': graph_features,
                'vgg_features': vgg_features,
                'fused_features': fused_features,
                'attention_weights': attention_weights
            }
            return logits, features_dict

        return logits
    
    def extract_features(self, fmri_data, smri_data):
        """
        仅提取特征，不进行分类
        
        返回:
        - features_dict: 包含各层特征的字典
        """
        with torch.no_grad():
            _, features_dict = self.forward(fmri_data, smri_data, return_features=True)
        return features_dict
    
    def get_attention_weights(self, fmri_data, smri_data):
        """
        获取注意力权重
        
        返回:
        - attention_weights: 注意力权重 [batch_size, 2]
        """
        with torch.no_grad():
            _, features_dict = self.forward(fmri_data, smri_data, return_features=True)
        return features_dict['attention_weights']


class MultiModalEnsemble(nn.Module):
    """
    多模态集成模型，结合多个多模态分类器
    """
    def __init__(self, models_configs, num_classes=2, ensemble_method='average'):
        """
        初始化多模态集成模型
        
        参数:
        - models_configs: 模型配置列表
        - num_classes: 分类类别数
        - ensemble_method: 集成方法 ('average', 'weighted', 'learned')
        """
        super(MultiModalEnsemble, self).__init__()
        
        self.num_models = len(models_configs)
        self.num_classes = num_classes
        self.ensemble_method = ensemble_method
        
        # 创建多个模型
        self.models = nn.ModuleList([
            MultiModalClassifier(**config) for config in models_configs
        ])
        
        # 学习的集成权重
        if ensemble_method == 'learned':
            self.ensemble_weights = nn.Parameter(torch.ones(self.num_models) / self.num_models)
        elif ensemble_method == 'weighted':
            # 可以手动设置权重
            self.register_buffer('ensemble_weights', torch.ones(self.num_models) / self.num_models)
    
    def forward(self, fmri_data, smri_data):
        """
        前向传播
        
        参数:
        - fmri_data: fMRI图数据
        - smri_data: sMRI 3D图像数据
        
        返回:
        - ensemble_logits: 集成后的分类logits
        """
        model_outputs = []
        
        # 获取所有模型的输出
        for model in self.models:
            logits = model(fmri_data, smri_data)
            model_outputs.append(logits)
        
        # 堆叠输出
        stacked_outputs = torch.stack(model_outputs, dim=0)  # [num_models, batch_size, num_classes]
        
        # 集成
        if self.ensemble_method == 'average':
            ensemble_logits = torch.mean(stacked_outputs, dim=0)
        elif self.ensemble_method in ['weighted', 'learned']:
            weights = F.softmax(self.ensemble_weights, dim=0)
            ensemble_logits = torch.sum(stacked_outputs * weights.view(-1, 1, 1), dim=0)
        else:
            raise ValueError(f"Unknown ensemble method: {self.ensemble_method}")
        
        return ensemble_logits


def create_multimodal_classifier(num_node_features,
                                graph_hidden_channels=64,
                                graph_feature_dim=128,
                                vgg_feature_dim=128,
                                fusion_dim=256,
                                num_classes=2,
                                use_gat=True,
                                gat_heads=8,
                                edge_dim=1):
    """
    创建多模态分类器的便捷函数

    参数:
    - num_node_features: 图神经网络节点特征维度
    - graph_hidden_channels: 图神经网络隐藏层维度
    - graph_feature_dim: 图神经网络特征维度
    - vgg_feature_dim: VGG特征维度
    - fusion_dim: 融合层维度
    - num_classes: 分类类别数
    - use_gat: 是否使用GAT而不是GCN
    - gat_heads: GAT注意力头数
    - edge_dim: 边特征维度

    返回:
    - MultiModalClassifier实例
    """
    return MultiModalClassifier(
        num_node_features=num_node_features,
        graph_hidden_channels=graph_hidden_channels,
        graph_feature_dim=graph_feature_dim,
        vgg_feature_dim=vgg_feature_dim,
        fusion_dim=fusion_dim,
        num_classes=num_classes,
        use_gat=use_gat,
        gat_heads=gat_heads,
        edge_dim=edge_dim
    )


def create_multimodal_ensemble(num_node_features, num_models=3, num_classes=2, use_gat=True):
    """
    创建多模态集成模型的便捷函数

    参数:
    - num_node_features: 图神经网络节点特征维度
    - num_models: 集成模型数量
    - num_classes: 分类类别数
    - use_gat: 是否使用GAT而不是GCN

    返回:
    - MultiModalEnsemble实例
    """
    # 创建不同配置的模型
    models_configs = []
    for i in range(num_models):
        config = {
            'num_node_features': num_node_features,
            'graph_hidden_channels': 64 + i * 16,  # 不同的隐藏层维度
            'graph_feature_dim': 128,
            'vgg_feature_dim': 128,
            'fusion_dim': 256 + i * 32,  # 不同的融合维度
            'num_classes': num_classes,
            'graph_dropout': 0.5 + i * 0.05,  # 不同的dropout率
            'use_gat': use_gat,
            'gat_heads': 8 - i * 2 if use_gat else 8,  # 不同的注意力头数
            'edge_dim': 1,
        }
        models_configs.append(config)

    return MultiModalEnsemble(
        models_configs=models_configs,
        num_classes=num_classes,
        ensemble_method='learned'
    )
