# GAT升级说明

## 概述

本项目已从使用GCN（图卷积网络）升级为使用GAT（图注意力网络），并充分利用边权重信息来提升模型性能。

## 主要改进

### 1. 从GCN到GAT的升级

**原始GCN特点：**
- 使用固定的邻域聚合方式
- 所有邻居节点的重要性相同
- 无法利用边权重信息

**新GAT特点：**
- 使用注意力机制动态计算邻居节点重要性
- 支持多头注意力机制
- 能够充分利用边权重信息
- 更强的表达能力和学习能力

### 2. 边权重信息的利用

**数据格式：**
- 边权重存储在`edge_lists/`目录下的NPZ文件中
- 每个文件包含`edge_index`和`edge_attr`
- `edge_attr`表示功能连接强度

**GAT中的应用：**
- 边权重作为额外的边特征输入到GAT层
- 注意力机制会考虑边权重来计算注意力分数
- 提供更精确的图表示学习

## 文件修改说明

### 1. `gcn_feature_extractor.py`
- 添加了`GATFeatureExtractor`类
- 添加了`GATClassifier`类
- 添加了便捷函数`create_gat_feature_extractor`和`create_gat_classifier`
- 保留了原有的GCN实现以保持向后兼容

### 2. `multimodal_model.py`
- 修改`MultiModalClassifier`支持GAT
- 添加GAT相关参数：`use_gat`, `gat_heads`, `edge_dim`等
- 前向传播中支持边权重传递
- 更新便捷函数支持GAT配置

### 3. `train_multimodal.py`
- 更新训练配置支持GAT参数
- 修改模型创建部分使用新的参数名称

### 4. 新增文件
- `test_gat_model.py`: GAT模型测试脚本
- `README_GAT_upgrade.md`: 本说明文档

## GAT模型参数

### 核心参数
- `num_node_features`: 节点特征维度
- `hidden_channels`: 隐藏层维度
- `feature_dim`: 输出特征维度
- `num_layers`: GAT层数
- `dropout`: Dropout率

### GAT特定参数
- `heads`: 注意力头数（默认8）
- `concat`: 是否拼接多头结果（默认True）
- `edge_dim`: 边特征维度（默认1）

### 使用示例

```python
# 创建GAT特征提取器
gat_extractor = create_gat_feature_extractor(
    num_node_features=4,
    hidden_channels=64,
    feature_dim=128,
    heads=8,
    edge_dim=1
)

# 创建多模态分类器（使用GAT）
model = create_multimodal_classifier(
    num_node_features=4,
    use_gat=True,
    gat_heads=8,
    edge_dim=1
)
```

## 性能对比

### 模型复杂度
- GAT参数数量: ~51,266
- GCN参数数量: ~50,498
- 增加约1.5%的参数量

### 功能增强
1. **注意力机制**: 动态学习邻居重要性
2. **边权重利用**: 充分利用功能连接强度信息
3. **多头注意力**: 学习多种类型的关系模式
4. **更强表达能力**: 理论上具有更强的图表示学习能力

## 训练配置更新

```python
config = {
    # 图神经网络参数
    'graph_hidden_channels': 64,
    'graph_feature_dim': 128,
    
    # GAT特定参数
    'use_gat': True,      # 使用GAT而不是GCN
    'gat_heads': 8,       # 注意力头数
    'edge_dim': 1,        # 边特征维度
    
    # 其他参数保持不变...
}
```

## 向后兼容性

- 保留了所有原有的GCN实现
- 可以通过设置`use_gat=False`来使用原始GCN
- 原有的训练脚本和配置仍然有效

## 测试验证

运行测试脚本验证GAT模型：

```bash
python test_gat_model.py
```

测试内容包括：
1. GAT特征提取器功能测试
2. GAT vs GCN性能对比
3. 多模态分类器测试
4. 边权重影响分析

## 预期改进

1. **更好的图表示**: GAT能够学习更精确的节点表示
2. **边权重利用**: 充分利用功能连接强度信息
3. **可解释性**: 注意力权重提供模型决策的可解释性
4. **泛化能力**: 多头注意力机制提升模型泛化能力

## 使用建议

1. **首次使用**: 建议使用默认的GAT配置开始训练
2. **参数调优**: 可以调整`gat_heads`和`hidden_channels`来优化性能
3. **对比实验**: 可以设置`use_gat=False`来对比GCN和GAT的性能
4. **边权重**: 确保数据中包含有效的边权重信息

## 注意事项

1. GAT模型参数略多于GCN，训练时间可能稍长
2. 需要确保输入数据包含`edge_attr`字段
3. 注意力头数应根据数据规模和计算资源调整
4. 建议在小数据集上先进行测试验证
