import os
import sys
import json
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from multimodal_dataset import MultiModalDataset, create_multimodal_transforms, multimodal_collate_fn
from multimodal_model import MultiModalClassifier, create_multimodal_classifier

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class MultiModalTrainer:
    """多模态模型训练器"""
    
    def __init__(self, config):
        """
        初始化训练器
        
        参数:
        - config: 训练配置字典
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 创建数据变换
        self.train_transform, self.val_transform = create_multimodal_transforms()
        
        # 加载数据集
        self.dataset = self._load_dataset()
        
        # 获取节点特征维度
        sample = self.dataset[0]
        self.num_node_features = sample['fmri'].x.shape[1]
        print(f"节点特征维度: {self.num_node_features}")
        print(f"数据集大小: {len(self.dataset)}")
        
    def _load_dataset(self):
        """加载多模态数据集"""
        dataset = MultiModalDataset(
            fmri_dir=self.config['fmri_dir'],
            smri_dir=self.config['smri_dir'],
            edge_lists_dir=self.config['edge_lists_dir'],
            node_features_dir=self.config['node_features_dir'],
            classes=self.config['classes'],
            transform_smri=None,  # 变换将在训练循环中应用
            use_edge_lists=self.config['use_edge_lists']
        )
        return dataset
    
    def train_fold(self, train_loader, val_loader, fold_num):
        """训练单个折"""
        print(f"\n=== 训练第 {fold_num} 折 ===")
        
        # 创建模型
        model = create_multimodal_classifier(
            num_node_features=self.num_node_features,
            graph_hidden_channels=self.config['graph_hidden_channels'],
            graph_feature_dim=self.config['graph_feature_dim'],
            vgg_feature_dim=self.config['vgg_feature_dim'],
            fusion_dim=self.config['fusion_dim'],
            num_classes=self.config['num_classes'],
            use_gat=self.config['use_gat'],
            gat_heads=self.config['gat_heads'],
            edge_dim=self.config['edge_dim']
        ).to(self.device)
        
        # 定义损失函数和优化器
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=self.config['learning_rate'], 
                             weight_decay=self.config['weight_decay'])
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='max', factor=0.5, patience=10, verbose=True
        )
        
        # 训练参数
        best_val_acc = 0.0
        patience_counter = 0
        train_losses = []
        val_accuracies = []
        
        for epoch in range(self.config['epochs']):
            # 训练阶段
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            train_bar = tqdm(train_loader, desc=f"Fold {fold_num} Epoch {epoch+1} Train")
            for batch in train_bar:
                # 应用训练变换到每个样本
                smri_batch = []
                for i in range(batch['smri'].shape[0]):
                    smri_sample = batch['smri'][i]  # [D, H, W]
                    smri_transformed = self.train_transform(smri_sample)  # 应用变换
                    smri_batch.append(smri_transformed)
                batch['smri'] = torch.stack(smri_batch)  # [batch_size, 1, D, H, W]

                # 移动到设备
                fmri_data = batch['fmri'].to(self.device)
                smri_data = batch['smri'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                # 前向传播
                optimizer.zero_grad()
                logits = model(fmri_data, smri_data)
                loss = criterion(logits, labels)
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                # 统计
                train_loss += loss.item()
                _, predicted = torch.max(logits.data, 1)
                train_total += labels.size(0)
                train_correct += (predicted == labels).sum().item()
                
                train_bar.set_postfix({
                    'loss': f"{loss.item():.4f}",
                    'acc': f"{100 * train_correct / train_total:.2f}%"
                })
            
            # 验证阶段
            val_acc, val_f1, val_loss = self.evaluate_model(model, val_loader, criterion)
            
            # 记录
            avg_train_loss = train_loss / len(train_loader)
            train_acc = 100 * train_correct / train_total
            
            train_losses.append(avg_train_loss)
            val_accuracies.append(val_acc)
            
            print(f"Epoch {epoch+1}/{self.config['epochs']}: "
                  f"Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc:.2f}%, "
                  f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Val F1: {val_f1:.4f}")
            
            # 学习率调度
            scheduler.step(val_acc)
            
            # 早停和模型保存
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                torch.save(model.state_dict(), f'best_multimodal_model_fold{fold_num}.pth')
                print(f"保存最佳模型，验证准确率: {val_acc:.4f}")
            else:
                patience_counter += 1
                if patience_counter >= self.config['patience']:
                    print(f"早停于第 {epoch+1} 轮")
                    break
        
        # 加载最佳模型
        model.load_state_dict(torch.load(f'best_multimodal_model_fold{fold_num}.pth'))
        
        return model, best_val_acc, train_losses, val_accuracies
    
    def evaluate_model(self, model, data_loader, criterion):
        """评估模型"""
        model.eval()
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for batch in data_loader:
                # 应用验证变换到每个样本
                smri_batch = []
                for i in range(batch['smri'].shape[0]):
                    smri_sample = batch['smri'][i]  # [D, H, W]
                    smri_transformed = self.val_transform(smri_sample)  # 应用变换
                    smri_batch.append(smri_transformed)
                batch['smri'] = torch.stack(smri_batch)  # [batch_size, 1, D, H, W]

                # 移动到设备
                fmri_data = batch['fmri'].to(self.device)
                smri_data = batch['smri'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                # 前向传播
                logits = model(fmri_data, smri_data)
                loss = criterion(logits, labels)
                
                # 统计
                total_loss += loss.item()
                _, predicted = torch.max(logits.data, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        # 计算指标
        avg_loss = total_loss / len(data_loader)
        accuracy = accuracy_score(all_labels, all_predictions) * 100
        f1 = f1_score(all_labels, all_predictions, average='macro')
        
        return accuracy, f1, avg_loss
    
    def cross_validation(self):
        """执行交叉验证"""
        print("开始交叉验证训练...")
        
        # 准备标签用于分层抽样
        labels = []
        for i in range(len(self.dataset)):
            labels.append(self.dataset[i]['label'].item())
        labels = np.array(labels)
        
        # 分层K折交叉验证
        skf = StratifiedKFold(n_splits=self.config['n_splits'], shuffle=True, random_state=42)
        
        fold_results = []
        all_predictions = []
        all_labels = []
        all_subject_results = []
        
        for fold, (train_idx, val_idx) in enumerate(skf.split(np.zeros(len(labels)), labels)):
            print(f"\n第 {fold+1}/{self.config['n_splits']} 折交叉验证")
            print(f"训练样本: {len(train_idx)}, 验证样本: {len(val_idx)}")
            
            # 创建数据子集
            train_subset = Subset(self.dataset, train_idx)
            val_subset = Subset(self.dataset, val_idx)
            
            # 创建数据加载器
            train_loader = DataLoader(
                train_subset, 
                batch_size=self.config['batch_size'], 
                shuffle=True,
                collate_fn=multimodal_collate_fn,
                num_workers=0
            )
            val_loader = DataLoader(
                val_subset, 
                batch_size=self.config['batch_size'], 
                shuffle=False,
                collate_fn=multimodal_collate_fn,
                num_workers=0
            )
            
            # 训练模型
            model, best_val_acc, train_losses, val_accuracies = self.train_fold(
                train_loader, val_loader, fold+1
            )
            
            # 最终评估
            final_acc, final_f1, _ = self.evaluate_model(model, val_loader, nn.CrossEntropyLoss())
            
            fold_results.append({
                'fold': fold + 1,
                'best_val_acc': best_val_acc,
                'final_acc': final_acc,
                'final_f1': final_f1,
                'train_losses': train_losses,
                'val_accuracies': val_accuracies
            })
            
            print(f"第 {fold+1} 折最终结果: 准确率 {final_acc:.4f}, F1分数 {final_f1:.4f}")
            
            # 收集预测结果用于总体评估
            model.eval()
            with torch.no_grad():
                for batch in val_loader:
                    batch['smri'] = self.val_transform(batch['smri'])
                    fmri_data = batch['fmri'].to(self.device)
                    smri_data = batch['smri'].to(self.device)
                    labels = batch['labels'].to(self.device)
                    
                    logits = model(fmri_data, smri_data)
                    _, predicted = torch.max(logits.data, 1)
                    
                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
                    
                    # 记录每个被试的结果
                    for i, subject_id in enumerate(batch['subject_ids']):
                        all_subject_results.append({
                            'subject_id': subject_id,
                            'true_label': labels[i].item(),
                            'predicted_label': predicted[i].item(),
                            'fold': fold + 1
                        })
        
        # 计算总体结果
        self._save_results(fold_results, all_predictions, all_labels, all_subject_results)
        
        return fold_results

    def _save_results(self, fold_results, all_predictions, all_labels, all_subject_results):
        """保存训练结果"""
        # 计算总体指标
        overall_acc = accuracy_score(all_labels, all_predictions)
        overall_f1 = f1_score(all_labels, all_predictions, average='macro')

        # 计算各折平均值
        fold_accs = [result['final_acc'] for result in fold_results]
        fold_f1s = [result['final_f1'] for result in fold_results]

        mean_acc = np.mean(fold_accs)
        std_acc = np.std(fold_accs)
        mean_f1 = np.mean(fold_f1s)
        std_f1 = np.std(fold_f1s)

        # 打印结果
        print('\n' + '='*50)
        print('交叉验证结果总结:')
        print('='*50)
        for i, result in enumerate(fold_results):
            print(f"第 {i+1} 折: 准确率 {result['final_acc']:.4f}, F1分数 {result['final_f1']:.4f}")

        print(f"\n平均准确率: {mean_acc:.4f} ± {std_acc:.4f}")
        print(f"平均F1分数: {mean_f1:.4f} ± {std_f1:.4f}")
        print(f"总体准确率: {overall_acc:.4f}")
        print(f"总体F1分数: {overall_f1:.4f}")

        # 保存详细结果
        results_summary = {
            'fold_results': fold_results,
            'overall_metrics': {
                'mean_accuracy': mean_acc,
                'std_accuracy': std_acc,
                'mean_f1': mean_f1,
                'std_f1': std_f1,
                'overall_accuracy': overall_acc,
                'overall_f1': overall_f1
            },
            'config': self.config
        }

        with open('multimodal_cv_results.json', 'w') as f:
            json.dump(results_summary, f, indent=2, default=str)

        # 保存被试级别结果
        subject_df = pd.DataFrame(all_subject_results)
        subject_df.to_csv('multimodal_subject_predictions.csv', index=False)

        # 生成混淆矩阵
        cm = confusion_matrix(all_labels, all_predictions)
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                    xticklabels=['HC', 'MCI'],
                    yticklabels=['HC', 'MCI'])
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.title('多模态模型总体混淆矩阵')
        plt.savefig('multimodal_confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 生成分类报告
        report = classification_report(all_labels, all_predictions,
                                     target_names=['HC', 'MCI'],
                                     output_dict=True)

        with open('multimodal_classification_report.json', 'w') as f:
            json.dump(report, f, indent=2)

        print(f"\n结果已保存到:")
        print(f"- multimodal_cv_results.json")
        print(f"- multimodal_subject_predictions.csv")
        print(f"- multimodal_confusion_matrix.png")
        print(f"- multimodal_classification_report.json")


def main():
    """主函数"""
    # 训练配置
    config = {
        # 数据路径
        'fmri_dir': './data_fmri',
        'smri_dir': './data_smri',
        'edge_lists_dir': './edge_lists',
        'node_features_dir': './node_features',
        'classes': ['HC', 'MCI'],
        'use_edge_lists': True,

        # 模型参数
        'graph_hidden_channels': 64,
        'graph_feature_dim': 128,
        'vgg_feature_dim': 128,
        'fusion_dim': 256,
        'num_classes': 2,

        # GAT特定参数
        'use_gat': True,  # 使用GAT而不是GCN
        'gat_heads': 8,   # 注意力头数
        'edge_dim': 1,    # 边特征维度

        # 训练参数
        'batch_size': 4,  # 由于数据量较小，使用较小的batch size
        'learning_rate': 0.001,
        'weight_decay': 1e-4,
        'epochs': 100,
        'patience': 20,
        'n_splits': 3,  # 3折交叉验证
    }

    print("多模态模型训练配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")

    # 创建训练器并开始训练
    trainer = MultiModalTrainer(config)
    fold_results = trainer.cross_validation()

    print("\n训练完成!")


if __name__ == "__main__":
    main()
